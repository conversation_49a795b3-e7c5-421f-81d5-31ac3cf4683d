namespace DrawnUi.Controls;

/// <summary>
/// Container for wheel items that provides triangular clipping for the classic wheel-of-names appearance.
/// Each item is clipped to a triangular segment of the wheel.
/// </summary>
public class SkiaWheelItemContainer : SkiaLayout
{
    public SkiaWheelItemContainer()
    {
        Type = LayoutType.Absolute;
    }

    #region BINDABLE PROPERTIES

    public static readonly BindableProperty ItemIndexProperty = BindableProperty.Create(
        nameof(ItemIndex),
        typeof(int),
        typeof(SkiaWheelItemContainer),
        0,
        propertyChanged: NeedDraw);

    public int ItemIndex
    {
        get => (int)GetValue(ItemIndexProperty);
        set => SetValue(ItemIndexProperty, value);
    }

    protected double WheelRadius { get; set; }

    protected override ScaledSize SetMeasured(float width, float height, bool widthCut, bool heightCut, float scale)
    {
        WheelRadius = (Math.Min(width, height) / 2.0) / scale;

        return base.SetMeasured(width, height, widthCut, heightCut, scale);
    }

    public static readonly BindableProperty UseTriangleClipProperty = BindableProperty.Create(
        nameof(UseTriangleClip),
        typeof(bool),
        typeof(SkiaWheelItemContainer),
        true,
        propertyChanged: NeedDraw);

    public bool UseTriangleClip
    {
        get => (bool)GetValue(UseTriangleClipProperty);
        set => SetValue(UseTriangleClipProperty, value);
    }

    protected int TotalItems
    {
        get
        {
            if (ItemsSource == null)
            {
                return 0;
            }

            return ItemsSource.Count;
        }
    }

    #endregion

    #region CLIPPING

    public override SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)
    {
        if (!UseTriangleClip || TotalItems == 0)
            return base.CreateClip(arguments, usePosition, path);

        path ??= new SKPath();
        path.Reset();

        var rect = DrawingRect;
        var center = new SKPoint(rect.MidX, rect.MidY);
        var radius = (float)WheelRadius;

        // Calculate the angle for this segment
        var anglePerItem = 360.0f / TotalItems;
        var startAngle = ItemIndex * anglePerItem - anglePerItem / 2;
        var endAngle = startAngle + anglePerItem;

        // Convert to radians
        var startRadians = startAngle * (float)Math.PI / 180f;
        var endRadians = endAngle * (float)Math.PI / 180f;

        // Create triangular segment path
        path.MoveTo(center);

        // Add arc from start to end angle
        var arcRect = new SKRect(center.X - radius, center.Y - radius, center.X + radius, center.Y + radius);
        path.ArcTo(arcRect, startAngle, anglePerItem, false);

        // Close the triangle back to center
        path.LineTo(center);
        path.Close();

        return path;
    }

    #endregion
}

/// <summary>
/// Enhanced SkiaWheelShape that creates triangle-clipped containers for items
/// </summary>
public class SkiaWheelShapeWithTriangles : SkiaWheelShape
{
    public static readonly BindableProperty UseTriangleClipProperty = BindableProperty.Create(
        nameof(UseTriangleClip),
        typeof(bool),
        typeof(SkiaWheelShapeWithTriangles),
        true,
        propertyChanged: NeedDraw);

    public bool UseTriangleClip
    {
        get => (bool)GetValue(UseTriangleClipProperty);
        set => SetValue(UseTriangleClipProperty, value);
    }

    public static readonly BindableProperty ItemColorsProperty = BindableProperty.Create(
        nameof(ItemColors),
        typeof(IList<Color>),
        typeof(SkiaWheelShapeWithTriangles),
        null,
        propertyChanged: NeedDraw);

    public IList<Color> ItemColors
    {
        get => (IList<Color>)GetValue(ItemColorsProperty);
        set => SetValue(ItemColorsProperty, value);
    }

    //protected override void OnDraw(DrawingContext ctx)
    //{
    //    // Draw the wheel background segments with different colors
    //    if (UseTriangleClip && Children.Count > 0)
    //    {
    //        DrawColoredSegments(ctx);
    //    }

    //    base.OnDraw(ctx);
    //}

    //void DrawColoredSegments(DrawingContext ctx)
    //{
    //    var center = new SKPoint(DrawingRect.MidX, DrawingRect.MidY);
    //    var radius = Math.Min(DrawingRect.Width, DrawingRect.Height) / 2.0f;
    //    var anglePerItem = 360.0f / Children.Count;

    //    var defaultColors = new[]
    //    {
    //        Colors.Red, Colors.Blue, Colors.Green, Colors.Yellow, Colors.Orange,
    //        Colors.Purple, Colors.Pink, Colors.Cyan, Colors.Lime, Colors.Magenta
    //    };

    //    for (int i = 0; i < Children.Count; i++)
    //    {
    //        var startAngle = i * anglePerItem - anglePerItem / 2 + (float)WheelRotation;

    //        // Choose color for this segment
    //        Color segmentColor;
    //        if (ItemColors != null && i < ItemColors.Count)
    //        {
    //            segmentColor = ItemColors[i];
    //        }
    //        else
    //        {
    //            segmentColor = defaultColors[i % defaultColors.Length];
    //        }

    //        using var paint = new SKPaint
    //        {
    //            Color = segmentColor.ToSKColor(),
    //            IsAntialias = true,
    //            Style = SKPaintStyle.Fill
    //        };

    //        using var path = new SKPath();
    //        path.MoveTo(center);

    //        var arcRect = new SKRect(center.X - radius, center.Y - radius, center.X + radius, center.Y + radius);
    //        path.ArcTo(arcRect, startAngle, anglePerItem, false);
    //        path.LineTo(center);
    //        path.Close();

    //        ctx.Canvas.DrawPath(path, paint);

    //        // Draw segment border
    //        using var strokePaint = new SKPaint
    //        {
    //            Color = Colors.White.ToSKColor(),
    //            IsAntialias = true,
    //            Style = SKPaintStyle.Stroke,
    //            StrokeWidth = 2
    //        };

    //        ctx.Canvas.DrawPath(path, strokePaint);
    //    }
    //}
}
