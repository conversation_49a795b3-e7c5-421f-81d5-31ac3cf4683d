using System.Numerics;

namespace DrawnUi.Controls;

/// <summary>
/// Custom SkiaShape that positions children in a circular arrangement around the wheel circumference.
/// Handles rotation and positioning calculations for the spinner wheel.
/// </summary>
public class SkiaWheelShape : SkiaShape, ISkiaGestureListener
{
    public SkiaWheelShape()
    {
        Type = ShapeType.Circle;
        RecyclingTemplate = RecyclingTemplate.Disabled;
    }

    #region BINDABLE PROPERTIES

    public static readonly BindableProperty WheelRotationProperty = BindableProperty.Create(
        nameof(WheelRotation),
        typeof(double),
        typeof(SkiaWheelShape),
        0.0,
        propertyChanged: NeedDraw);

    public double WheelRotation
    {
        get => (double)GetValue(WheelRotationProperty);
        set => SetValue(WheelRotationProperty, value);
    }

    public static readonly BindableProperty WheelRadiusProperty = BindableProperty.Create(
        nameof(WheelRadius),
        typeof(double),
        typeof(SkiaWheelShape),
        100.0,
        propertyChanged: NeedDraw);

    public double WheelRadius
    {
        get => (double)GetValue(WheelRadiusProperty);
        set => SetValue(WheelRadiusProperty, value);
    }

    #endregion

    #region GESTURE HANDLING

    ScrollFlingAnimator _flingAnimator;
    bool _isUserPanning;
    double _lastPanAngle;
    double _panStartRotation;

    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        if (args.Type == TouchActionResult.Down)
        {
            _flingAnimator?.Stop();
            _isUserPanning = true;
            _panStartRotation = WheelRotation;
            _lastPanAngle = GetAngleFromPoint(args.Event.Location);
            return this;
        }

        if (args.Type == TouchActionResult.Panning && _isUserPanning)
        {
            var currentAngle = GetAngleFromPoint(args.Event.Location);
            var deltaAngle = currentAngle - _lastPanAngle;
            
            // Handle angle wrapping
            if (deltaAngle > 180) deltaAngle -= 360;
            if (deltaAngle < -180) deltaAngle += 360;
            
            WheelRotation += deltaAngle;
            _lastPanAngle = currentAngle;
            
            return this;
        }

        if (args.Type == TouchActionResult.Up && _isUserPanning)
        {
            _isUserPanning = false;
            
            // Start fling animation if there's sufficient velocity
            var velocity = args.Event.Distance.Velocity;
            var angularVelocity = GetAngularVelocity(velocity);
            
            if (Math.Abs(angularVelocity) > 50) // Minimum velocity threshold
            {
                StartFlingAnimation(angularVelocity);
            }
            else
            {
                SnapToNearestItem();
            }
            
            return null;
        }

        return base.ProcessGestures(args, apply);
    }

    double GetAngleFromPoint(PointF point)
    {
        var center = new SKPoint(DrawingRect.MidX, DrawingRect.MidY);
        var dx = point.X - center.X;
        var dy = point.Y - center.Y;
        return Math.Atan2(dy, dx) * 180.0 / Math.PI;
    }

    double GetAngularVelocity(PointF velocity)
    {
        var center = new SKPoint(DrawingRect.MidX, DrawingRect.MidY);
        var radius = Math.Min(DrawingRect.Width, DrawingRect.Height) / 2.0;
        var linearVelocity = Math.Sqrt(velocity.X * velocity.X + velocity.Y * velocity.Y);
        return linearVelocity / radius * 180.0 / Math.PI; // Convert to degrees per second
    }

    void StartFlingAnimation(double angularVelocity)
    {
        if (_flingAnimator == null)
        {
            _flingAnimator = new ScrollFlingAnimator(this);
        }

        var deceleration = 0.95f; // Adjust for desired friction
        var threshold = 1.0f; // Stop when velocity is below this threshold

        _flingAnimator.OnUpdated = (value) =>
        {
            WheelRotation = value;
        };

        _flingAnimator.OnStop = () =>
        {
            SnapToNearestItem();
        };

        _flingAnimator.InitializeWithVelocity((float)WheelRotation, (float)angularVelocity, deceleration, threshold);
        _flingAnimator.Start();
    }

    void SnapToNearestItem()
    {
        if (Children.Count == 0) return;

        var anglePerItem = 360.0 / Children.Count;
        var normalizedRotation = ((WheelRotation % 360) + 360) % 360;
        var nearestIndex = Math.Round(normalizedRotation / anglePerItem);
        var targetRotation = nearestIndex * anglePerItem;

        // Animate to the nearest position
        var animator = new RangeAnimator(this);
        animator.OnUpdated = (value) => WheelRotation = value;
        animator.Start(value => { WheelRotation = value; }, WheelRotation, targetRotation, 300, Easing.CubicOut);
    }

    #endregion

    #region LAYOUT AND DRAWING

    protected override int RenderViewsList(DrawingContext context, IEnumerable<SkiaControl> skiaControls)
    {
        if (skiaControls == null)
            return 0;
        var count = 0;

        List<SkiaControlWithRect> tree = new();
        var childrenList = skiaControls.ToList();

        //actually base control has NO virtualization on purpose. implemented only for layouts.
        foreach (var child in childrenList)
        {
            if (child != null)
            {
                child.OptionalOnBeforeDrawing(); //could set IsVisible or whatever inside
                if (child.CanDraw) //still visible
                {
                    // Apply wheel transforms before rendering
                    var childIndex = childrenList.IndexOf(child);
                    if (childIndex >= 0 && childrenList.Count > 0)
                    {
                        // Calculate wheel properties
                        var radius = Math.Min(DrawingRect.Width, DrawingRect.Height) / 2.0;
                        var itemRadius = radius * 0.7; // Position items at 70% of wheel radius

                        var anglePerItem = 360.0 / childrenList.Count;
                        var childAngle = (childIndex * anglePerItem + WheelRotation) * Math.PI / 180.0;

                        // Calculate position offset from center
                        var offsetX = Math.Cos(childAngle) * itemRadius;
                        var offsetY = Math.Sin(childAngle) * itemRadius;

                        // Set child position using DrawnUI properties
                        child.TranslationX = offsetX;
                        child.TranslationY = offsetY;

                        // Set child rotation for readability
                        var textRotation = childAngle * 180.0 / Math.PI + 90; // +90 to make text perpendicular to radius
                        if (textRotation > 90 && textRotation < 270)
                        {
                            textRotation += 180; // Flip text if it would be upside down
                        }
                        child.Rotation = textRotation;
                    }

                    child.Render(context);

                    tree.Add(new SkiaControlWithRect(child,
                        context.Destination,
                        child.LastDrawnAt,
                        count));

                    count++;
                }
            }
        }

        SetRenderingTree(tree);

        return count;
    }



    #endregion
}
