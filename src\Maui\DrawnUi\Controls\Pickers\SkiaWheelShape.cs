using System.Numerics;

namespace DrawnUi.Controls;

/// <summary>
/// Custom SkiaShape that positions children in a circular arrangement around the wheel circumference.
/// Handles rotation and positioning calculations for the spinner wheel.
/// </summary>
public class SkiaWheelShape : SkiaShape, ISkiaGestureListener
{
    public SkiaWheelShape()
    {
        Type = ShapeType.Circle;
        RecyclingTemplate = RecyclingTemplate.Disabled;
    }

    #region BINDABLE PROPERTIES

    public static readonly BindableProperty WheelRotationProperty = BindableProperty.Create(
        nameof(WheelRotation),
        typeof(double),
        typeof(SkiaWheelShape),
        0.0,
        propertyChanged: NeedDraw);

    public double WheelRotation
    {
        get => (double)GetValue(WheelRotationProperty);
        set => SetValue(WheelRotationProperty, value);
    }

    public static readonly BindableProperty WheelRadiusProperty = BindableProperty.Create(
        nameof(WheelRadius),
        typeof(double),
        typeof(SkiaWheelShape),
        100.0,
        propertyChanged: NeedDraw);

    public double WheelRadius
    {
        get => (double)GetValue(WheelRadiusProperty);
        set => SetValue(WheelRadiusProperty, value);
    }

    #endregion

    #region GESTURE HANDLING

    ScrollFlingAnimator _flingAnimator;
    bool _isUserPanning;
    double _lastPanAngle;
    double _panStartRotation;

    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        if (args.Type == TouchActionResult.Down)
        {
            _flingAnimator?.Stop();
            _isUserPanning = true;
            _panStartRotation = WheelRotation;
            _lastPanAngle = GetAngleFromPoint(args.Event.Location);
            return this;
        }

        if (args.Type == TouchActionResult.Panning && _isUserPanning)
        {
            var currentAngle = GetAngleFromPoint(args.Event.Location);
            var deltaAngle = currentAngle - _lastPanAngle;
            
            // Handle angle wrapping
            if (deltaAngle > 180) deltaAngle -= 360;
            if (deltaAngle < -180) deltaAngle += 360;
            
            WheelRotation += deltaAngle;
            _lastPanAngle = currentAngle;
            
            return this;
        }

        if (args.Type == TouchActionResult.Up && _isUserPanning)
        {
            _isUserPanning = false;
            
            // Start fling animation if there's sufficient velocity
            var velocity = args.Event.Distance.Velocity;
            var angularVelocity = GetAngularVelocity(velocity);
            
            if (Math.Abs(angularVelocity) > 50) // Minimum velocity threshold
            {
                StartFlingAnimation(angularVelocity);
            }
            else
            {
                SnapToNearestItem();
            }
            
            return null;
        }

        return base.ProcessGestures(args, apply);
    }

    double GetAngleFromPoint(PointF point)
    {
        var center = new SKPoint(DrawingRect.MidX, DrawingRect.MidY);
        var dx = point.X - center.X;
        var dy = point.Y - center.Y;
        return Math.Atan2(dy, dx) * 180.0 / Math.PI;
    }

    double GetAngularVelocity(PointF velocity)
    {
        var center = new SKPoint(DrawingRect.MidX, DrawingRect.MidY);
        var radius = Math.Min(DrawingRect.Width, DrawingRect.Height) / 2.0;
        var linearVelocity = Math.Sqrt(velocity.X * velocity.X + velocity.Y * velocity.Y);
        return linearVelocity / radius * 180.0 / Math.PI; // Convert to degrees per second
    }

    void StartFlingAnimation(double angularVelocity)
    {
        if (_flingAnimator == null)
        {
            _flingAnimator = new ScrollFlingAnimator(this);
        }

        var deceleration = 0.95f; // Adjust for desired friction
        var threshold = 1.0f; // Stop when velocity is below this threshold

        _flingAnimator.OnUpdated = (value) =>
        {
            WheelRotation = value;
        };

        _flingAnimator.OnStop = () =>
        {
            SnapToNearestItem();
        };

        _flingAnimator.InitializeWithVelocity((float)WheelRotation, (float)angularVelocity, deceleration, threshold);
        _flingAnimator.Start();
    }

    void SnapToNearestItem()
    {
        if (Children.Count == 0) return;

        var anglePerItem = 360.0 / Children.Count;
        var normalizedRotation = ((WheelRotation % 360) + 360) % 360;
        var nearestIndex = Math.Round(normalizedRotation / anglePerItem);
        var targetRotation = nearestIndex * anglePerItem;

        // Animate to the nearest position
        var animator = new RangeAnimator(this);
        animator.OnUpdated = (value) => WheelRotation = value;
        animator.Start(value => { WheelRotation = value; }, WheelRotation, targetRotation, 300, Easing.CubicOut);
    }

    #endregion

    #region LAYOUT AND DRAWING

    protected override bool DrawChild(DrawingContext ctx, ISkiaControl child)
    {
        if (child is not SkiaControl control || !child.CanDraw)
            return false;

        var childIndex = Children.IndexOf(child as SkiaControl);
        if (childIndex < 0) return false;

        if (Children.Count == 0) return false;

        // Calculate wheel properties
        var centerX = DrawingRect.MidX;
        var centerY = DrawingRect.MidY;
        var radius = Math.Min(DrawingRect.Width, DrawingRect.Height) / 2.0;

        var anglePerItem = 360.0 / Children.Count;
        var childAngle = (childIndex * anglePerItem + WheelRotation) * Math.PI / 180.0;

        var saved = ctx.Context.Canvas.Save();

        // Create triangular clip path for this wheel segment
        using var clipPath = new SKPath();
        clipPath.MoveTo((float)centerX, (float)centerY); // Center point

        // Calculate start and end angles for the triangular segment
        var startAngleDeg = childIndex * anglePerItem + WheelRotation;
        var endAngleDeg = (childIndex + 1) * anglePerItem + WheelRotation;

        // Add arc for the outer edge of the segment
        var rect = new SKRect(
            (float)(centerX - radius),
            (float)(centerY - radius),
            (float)(centerX + radius),
            (float)(centerY + radius));

        clipPath.ArcTo(rect, (float)startAngleDeg, (float)anglePerItem, false);
        clipPath.Close();

        // Apply the clip
        ctx.Context.Canvas.ClipPath(clipPath);

        // Calculate position for this child around the wheel circumference
        var itemRadius = radius * 0.7; // Position items at 70% of wheel radius
        var x = centerX + Math.Cos(childAngle) * itemRadius;
        var y = centerY + Math.Sin(childAngle) * itemRadius;

        // Apply rotation and translation to position the child
        ctx.Context.Canvas.Translate((float)x, (float)y);

        // Rotate the child so text is readable (perpendicular to radius)
        var textRotation = childAngle * 180.0 / Math.PI + 90; // +90 to make text perpendicular to radius
        if (textRotation > 90 && textRotation < 270)
        {
            textRotation += 180; // Flip text if it would be upside down
        }
        ctx.Context.Canvas.RotateDegrees((float)textRotation);

        // Calculate the child's destination rectangle centered at origin (since we translated)
        var childSize = child.MeasuredSize.Pixels;
        var childRect = new SKRect(
            (float)(-childSize.Width / 2),
            (float)(-childSize.Height / 2),
            (float)(childSize.Width / 2),
            (float)(childSize.Height / 2));

        // Render the child at the transformed position
        child.Render(ctx.WithDestination(childRect));

        ctx.Context.Canvas.RestoreToCount(saved);

        return true;
    }

    #endregion
}
