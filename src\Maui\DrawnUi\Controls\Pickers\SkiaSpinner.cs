using System.Collections;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace DrawnUi.Controls;

/// <summary>
/// A wheel-of-names spinner control that displays items in a circular arrangement
/// and allows spinning to select an item through gesture interaction.
/// </summary>
[ContentProperty("ItemTemplate")]
public class SkiaSpinner : SkiaLayout
{
    public SkiaSpinner()
    {
        CreateUi();
    }

    #region BINDABLE PROPERTIES

    public override void ResetItemsSource()
    {
        SyncItemsSource();
    }

    public override void OnItemSourceChanged()
    {
        SyncItemsSource();
    }

    protected override void ItemsSourceCollectionChanged(object sender, NotifyCollectionChangedEventArgs args)
    {
        SyncItemsSource();
    }

    void SyncItemsSource()
    {
        if (_wheelShape != null)
        {
            _wheelShape.ItemsSource = this.ItemsSource;
            _wheelShape.ItemTemplate = this.ItemTemplate ?? DefaultTemplate;
        }
    }

    public static readonly BindableProperty SelectedIndexProperty = BindableProperty.Create(
        nameof(SelectedIndex),
        typeof(int),
        typeof(SkiaSpinner),
        -1,
        BindingMode.TwoWay,
        propertyChanged: OnSelectedIndexChanged);

    public int SelectedIndex
    {
        get => (int)GetValue(SelectedIndexProperty);
        set => SetValue(SelectedIndexProperty, value);
    }

    public static readonly BindableProperty WheelRotationProperty = BindableProperty.Create(
        nameof(WheelRotation),
        typeof(double),
        typeof(SkiaSpinner),
        0.1,
        propertyChanged: OnWheelRotationChanged);

    public double WheelRotation
    {
        get => (double)GetValue(WheelRotationProperty);
        set => SetValue(WheelRotationProperty, value);
    }


    static Color[] DefaultColors = new[]
    {
        Colors.Red, Colors.Blue, Colors.Green, Colors.Yellow, Colors.Orange, Colors.Purple, Colors.Pink,
        Colors.Cyan, Colors.Lime, Colors.Magenta
    };

    private static DataTemplate DefaultTemplate = new DataTemplate(() =>
    {
        var cell = new SkiaMarkdownLabel()
        {
            UseCache = SkiaCacheType.Image,
            Padding = 16,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Center,
            HorizontalTextAlignment = DrawTextAlignment.Center,
            VerticalTextAlignment = TextAlignment.Center
        }.ObserveSelf((me, prop) =>
        {
            if (prop == nameof(BindingContext))
            {
                me.Text = me.BindingContext as string;
                me.BackgroundColor = DefaultColors[Random.Next(0, DefaultColors.Length - 1)];
            }
        });

        return cell;
    });

    public new DataTemplate ItemTemplate
    {
        get => (DataTemplate)GetValue(ItemTemplateProperty);
        set => SetValue(ItemTemplateProperty, value);
    }

    public static readonly BindableProperty WheelRadiusProperty = BindableProperty.Create(
        nameof(WheelRadius),
        typeof(double),
        typeof(SkiaSpinner),
        100.0,
        propertyChanged: NeedDraw);

    public double WheelRadius
    {
        get => (double)GetValue(WheelRadiusProperty);
        set => SetValue(WheelRadiusProperty, value);
    }

    #endregion

    #region EVENTS

    public event EventHandler<int> SelectedIndexChanged;

    #endregion

    #region PRIVATE FIELDS

    SkiaWheelShape _wheelShape;

    //readonly List<object> _itemsList = new();
    bool _isUpdatingFromRotation;

    #endregion

    #region PROPERTY CHANGED HANDLERS

    static void OnSelectedIndexChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner control && !control._isUpdatingFromRotation)
        {
            control.UpdateWheelRotationFromIndex();
            control.SelectedIndexChanged?.Invoke(control, (int)newValue);
        }
    }

    static void OnWheelRotationChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner spinner)
        {
            spinner.UpdateSelectedIndexFromRotation();
            spinner._wheelShape?.Update();
        }
    }

    #endregion

    #region PRIVATE METHODS

    new void CreateUi()
    {
        _wheelShape = new SkiaWheelShape
        {
            Type = ShapeType.Circle,
            BackgroundColor = Colors.LightGray,
            StrokeColor = Colors.DarkGray,
            StrokeWidth = 2,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill
        };

        // Bind the wheel rotation between spinner and wheel shape
        _wheelShape.SetBinding(SkiaWheelShape.WheelRotationProperty,
            new Binding(nameof(WheelRotation), source: this));
        _wheelShape.SetBinding(SkiaWheelShape.WheelRadiusProperty,
            new Binding(nameof(WheelRadius), source: this));

        Children = new List<SkiaControl>() { _wheelShape };
    }


    protected int ItemsCount
    {
        get
        {
            if (ItemsSource == null)
            {
                return 0;
            }
            return ItemsSource.Count;
        }
    }

    void UpdateWheelRotationFromIndex()
    {
        if (ItemsCount == 0) return;

        var anglePerItem = 360.0 / ItemsCount;
        var targetRotation = -SelectedIndex * anglePerItem;

        if (Math.Abs(WheelRotation - targetRotation) > 0.1)
        {
            WheelRotation = targetRotation;
        }
    }

    void UpdateSelectedIndexFromRotation()
    {
        if (ItemsCount == 0) return;

        _isUpdatingFromRotation = true;

        var anglePerItem = 360.0 / ItemsCount;
        var normalizedRotation = ((WheelRotation % 360) + 360) % 360;
        var index = (int)Math.Round(normalizedRotation / anglePerItem) % ItemsCount;

        if (SelectedIndex != index)
        {
            SelectedIndex = index;
        }

        _isUpdatingFromRotation = false;
    }

    #endregion

    #region PUBLIC METHODS

    /// <summary>
    /// Spins the wheel to a random position with animation
    /// </summary>
    public void SpinToRandom()
    {
        if (ItemsCount == 0) return;

        var random = new Random();
        var randomIndex = random.Next(ItemsCount);
        var extraSpins = random.Next(3, 8); // 3-7 full rotations for dramatic effect

        var anglePerItem = 360.0 / ItemsCount;
        var targetRotation = -(randomIndex * anglePerItem) + (extraSpins * 360);

        Rotate(targetRotation, (uint)(2000 + random.Next(1000))); // 2-3 seconds
    }

    /// <summary>
    /// Animates the wheel to a specific rotation
    /// </summary>
    public void Rotate(double targetRotation, uint durationMs = 500)
    {
        var animator = new RangeAnimator(this);
        animator.OnUpdated = (value) => WheelRotation = value;
        animator.Start(value => { WheelRotation = value; }, WheelRotation, targetRotation, durationMs, Easing.CubicOut);
    }

    #endregion
}
