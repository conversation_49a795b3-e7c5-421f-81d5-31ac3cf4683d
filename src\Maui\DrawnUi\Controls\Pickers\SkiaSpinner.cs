using System.Collections;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace DrawnUi.Controls;

/// <summary>
/// A wheel-of-names spinner control that displays items in a circular arrangement
/// and allows spinning to select an item through gesture interaction.
/// </summary>
[ContentProperty("ItemTemplate")]
public class SkiaSpinner : SkiaControl
{
    public SkiaSpinner()
    {
        CreateDefaultContent();
    }

    #region BINDABLE PROPERTIES

    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(
        nameof(ItemsSource),
        typeof(IEnumerable),
        typeof(SkiaSpinner),
        null,
        propertyChanged: OnItemsSourceChanged);

    public IEnumerable ItemsSource
    {
        get => (IEnumerable)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    public static readonly BindableProperty SelectedIndexProperty = BindableProperty.Create(
        nameof(SelectedIndex),
        typeof(int),
        typeof(SkiaSpinner),
        -1,
        BindingMode.TwoWay,
        propertyChanged: OnSelectedIndexChanged);

    public int SelectedIndex
    {
        get => (int)GetValue(SelectedIndexProperty);
        set => SetValue(SelectedIndexProperty, value);
    }

    public static readonly BindableProperty WheelRotationProperty = BindableProperty.Create(
        nameof(WheelRotation),
        typeof(double),
        typeof(SkiaSpinner),
        0.0,
        propertyChanged: OnWheelRotationChanged);

    public double WheelRotation
    {
        get => (double)GetValue(WheelRotationProperty);
        set => SetValue(WheelRotationProperty, value);
    }

    public static readonly BindableProperty ItemTemplateProperty = BindableProperty.Create(
        nameof(ItemTemplate),
        typeof(DataTemplate),
        typeof(SkiaSpinner),
        null,
        propertyChanged: OnItemTemplateChanged);

    public DataTemplate ItemTemplate
    {
        get => (DataTemplate)GetValue(ItemTemplateProperty);
        set => SetValue(ItemTemplateProperty, value);
    }

    public static readonly BindableProperty WheelRadiusProperty = BindableProperty.Create(
        nameof(WheelRadius),
        typeof(double),
        typeof(SkiaSpinner),
        100.0,
        propertyChanged: NeedDraw);

    public double WheelRadius
    {
        get => (double)GetValue(WheelRadiusProperty);
        set => SetValue(WheelRadiusProperty, value);
    }

    #endregion

    #region EVENTS

    public event EventHandler<int> SelectedIndexChanged;

    #endregion

    #region PRIVATE FIELDS

    SkiaWheelShape _wheelShape;
    readonly List<object> _itemsList = new();
    bool _isUpdatingFromRotation;

    #endregion

    #region PROPERTY CHANGED HANDLERS

    static void OnItemsSourceChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner spinner)
        {
            spinner.UpdateItemsList();
            spinner.RecreateWheelItems();
        }
    }

    static void OnSelectedIndexChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner spinner && !spinner._isUpdatingFromRotation)
        {
            spinner.UpdateWheelRotationFromIndex();
            spinner.SelectedIndexChanged?.Invoke(spinner, (int)newValue);
        }
    }

    static void OnWheelRotationChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner spinner)
        {
            spinner.UpdateSelectedIndexFromRotation();
            spinner._wheelShape?.Update();
        }
    }

    static void OnItemTemplateChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SkiaSpinner spinner)
        {
            spinner.RecreateWheelItems();
        }
    }

    #endregion

    #region PRIVATE METHODS

    void CreateDefaultContent()
    {
        _wheelShape = new SkiaWheelShape
        {
            Type = ShapeType.Circle,
            BackgroundColor = Colors.LightGray,
            StrokeColor = Colors.DarkGray,
            StrokeWidth = 2,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill
        };

        // Bind the wheel rotation between spinner and wheel shape
        _wheelShape.SetBinding(SkiaWheelShape.WheelRotationProperty,
            new Binding(nameof(WheelRotation), source: this));
        _wheelShape.SetBinding(SkiaWheelShape.WheelRadiusProperty,
            new Binding(nameof(WheelRadius), source: this));

        Content = _wheelShape;
    }

    void UpdateItemsList()
    {
        _itemsList.Clear();
        
        if (ItemsSource != null)
        {
            foreach (var item in ItemsSource)
            {
                _itemsList.Add(item);
            }
        }

        // Validate item count constraints
        if (_itemsList.Count < 2)
        {
            // Add default items if less than minimum
            while (_itemsList.Count < 2)
            {
                _itemsList.Add($"Item {_itemsList.Count + 1}");
            }
        }
        else if (_itemsList.Count > 150)
        {
            // Trim to maximum
            _itemsList.RemoveRange(150, _itemsList.Count - 150);
        }
    }

    void RecreateWheelItems()
    {
        if (_wheelShape == null) return;

        _wheelShape.Children.Clear();

        for (int i = 0; i < _itemsList.Count; i++)
        {
            var item = _itemsList[i];
            SkiaControl itemView;

            if (ItemTemplate != null)
            {
                itemView = (SkiaControl)ItemTemplate.CreateContent();
                itemView.BindingContext = item;
            }
            else
            {
                // Default template - simple label
                itemView = new SkiaLabel
                {
                    Text = item?.ToString() ?? string.Empty,
                    TextColor = Colors.Black,
                    FontSize = 14,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                };
            }

            _wheelShape.Children.Add(itemView);
        }

        UpdateWheelRotationFromIndex();
    }

    void UpdateWheelRotationFromIndex()
    {
        if (_itemsList.Count == 0) return;

        var anglePerItem = 360.0 / _itemsList.Count;
        var targetRotation = -SelectedIndex * anglePerItem;
        
        if (Math.Abs(WheelRotation - targetRotation) > 0.1)
        {
            WheelRotation = targetRotation;
        }
    }

    void UpdateSelectedIndexFromRotation()
    {
        if (_itemsList.Count == 0) return;

        _isUpdatingFromRotation = true;

        var anglePerItem = 360.0 / _itemsList.Count;
        var normalizedRotation = ((WheelRotation % 360) + 360) % 360;
        var index = (int)Math.Round(normalizedRotation / anglePerItem) % _itemsList.Count;

        if (SelectedIndex != index)
        {
            SelectedIndex = index;
        }

        _isUpdatingFromRotation = false;
    }

    #endregion

    #region PUBLIC METHODS

    /// <summary>
    /// Spins the wheel to a random position with animation
    /// </summary>
    public void SpinToRandom()
    {
        if (_itemsList.Count == 0) return;

        var random = new Random();
        var randomIndex = random.Next(_itemsList.Count);
        var extraSpins = random.Next(3, 8); // 3-7 full rotations for dramatic effect

        var anglePerItem = 360.0 / _itemsList.Count;
        var targetRotation = -(randomIndex * anglePerItem) + (extraSpins * 360);

        AnimateToRotation(targetRotation, 2000 + random.Next(1000)); // 2-3 seconds
    }

    /// <summary>
    /// Animates the wheel to a specific rotation
    /// </summary>
    public void AnimateToRotation(double targetRotation, uint durationMs = 500)
    {
        var animator = new RangeAnimator(this);
        animator.OnUpdated = (value) => WheelRotation = value;
        animator.Start(WheelRotation, targetRotation, durationMs, Easing.CubicOut);
    }

    #endregion
}
